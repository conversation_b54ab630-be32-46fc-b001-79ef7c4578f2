// ===== CONFIGURATION SÉCURISÉE =====
// Ce fichier contient les configurations sensibles encodées pour plus de sécurité

// Configuration de sécurité
const SECURITY_CONFIG = {
    // Avertissement de sécurité
    WARNING: {
        message: "⚠️ AVERTISSEMENT DE SÉCURITÉ",
        details: [
            "Les clés API et identifiants sont encodés en Base64 pour masquer leur visibilité directe.",
            "Cette méthode offre une sécurité basique mais n'est PAS suffisante pour un environnement de production.",
            "Pour une sécurité renforcée en production :",
            "1. Utilisez des variables d'environnement côté serveur",
            "2. Implémentez un proxy backend pour masquer les clés API",
            "3. Ajoutez une authentification utilisateur robuste",
            "4. Utilisez HTTPS et des certificats SSL valides",
            "5. Implémentez une limitation de taux (rate limiting)"
        ]
    },

    // Niveau de sécurité actuel
    SECURITY_LEVEL: "BASIC_OBFUSCATION",
    
    // Recommandations pour la production
    PRODUCTION_RECOMMENDATIONS: {
        backend_proxy: "Créer un endpoint backend qui fait les appels API",
        environment_variables: "Stocker les clés dans des variables d'environnement",
        authentication: "Implémenter OAuth2 ou JWT pour l'authentification",
        rate_limiting: "Limiter le nombre de requêtes par utilisateur",
        monitoring: "Ajouter des logs et monitoring des accès"
    }
};

// Configuration de l'API Gemini (encodée)
const GEMINI_API_CONFIG = {
    // Clé API encodée en Base64 (décodage requis)
    encoded_key: 'QUl6YVN5QjlWNDVnNUF4LVZvcWp3MUo3WDYxUDJnSnFrV2FnU180',
    
    // URL de l'API
    base_url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent',
    
    // Paramètres de configuration
    max_retries: 3,
    retry_delay: 1000,
    timeout: 30000,
    
    // Limites de sécurité
    max_message_length: 2000,
    max_history_size: 50,
    
    // Headers par défaut
    default_headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'BTK-Calculator/1.0'
    }
};

// Configuration des identifiants admin (encodée)
const ADMIN_AUTH_CONFIG = {
    // Identifiants encodés en Base64
    credentials: {
        username: 'YWRtaW5hZG1pbg==', // adminadmin encodé
        password: 'YWRtaW4yMDAy'       // admin2002 encodé
    },
    
    // Paramètres de session
    session: {
        timeout: 30 * 60 * 1000, // 30 minutes
        auto_logout_warning: 5 * 60 * 1000, // Avertir 5 min avant expiration
        max_failed_attempts: 3,
        lockout_duration: 15 * 60 * 1000 // 15 minutes de verrouillage
    },
    
    // Permissions admin
    permissions: {
        view_stats: true,
        export_data: true,
        clear_data: true,
        manage_users: false // Désactivé pour cette version
    }
};

// Configuration de logging pour la sécurité
const SECURITY_LOGGING = {
    enabled: true,
    log_failed_attempts: true,
    log_api_calls: false, // Désactivé pour éviter de logger les clés
    log_admin_actions: true,
    
    // Types d'événements à logger
    events: {
        admin_login: true,
        admin_logout: true,
        failed_login: true,
        api_error: true,
        security_violation: true
    }
};

// Fonction utilitaire pour décoder de manière sécurisée
function secureDecodeConfig(encodedValue, configType = 'general') {
    try {
        const decoded = atob(encodedValue);
        
        // Log de sécurité (sans révéler la valeur)
        if (SECURITY_LOGGING.enabled) {
            console.log(`🔐 Configuration ${configType} décodée avec succès`);
        }
        
        return decoded;
    } catch (error) {
        console.error(`❌ Erreur lors du décodage de la configuration ${configType}:`, error);
        return null;
    }
}

// Fonction pour valider l'intégrité de la configuration
function validateSecurityConfig() {
    const checks = {
        gemini_key: GEMINI_API_CONFIG.encoded_key && GEMINI_API_CONFIG.encoded_key.length > 0,
        admin_credentials: ADMIN_AUTH_CONFIG.credentials.username && ADMIN_AUTH_CONFIG.credentials.password,
        api_url: GEMINI_API_CONFIG.base_url && GEMINI_API_CONFIG.base_url.startsWith('https://'),
        security_params: ADMIN_AUTH_CONFIG.session.timeout > 0
    };
    
    const allValid = Object.values(checks).every(check => check === true);
    
    if (SECURITY_LOGGING.enabled) {
        console.log('🔍 Validation de la configuration de sécurité:', {
            status: allValid ? 'VALIDE' : 'ERREUR',
            checks: Object.keys(checks).filter(key => !checks[key])
        });
    }
    
    return allValid;
}

// Fonction pour afficher les avertissements de sécurité
function displaySecurityWarnings() {
    if (SECURITY_CONFIG.SECURITY_LEVEL === 'BASIC_OBFUSCATION') {
        console.warn('⚠️ AVERTISSEMENT DE SÉCURITÉ ⚠️');
        console.warn('Cette application utilise un niveau de sécurité basique.');
        console.warn('Pour la production, veuillez implémenter les recommandations de sécurité.');
        console.warn('Consultez SECURITY_CONFIG.PRODUCTION_RECOMMENDATIONS pour plus de détails.');
    }
}

// Initialisation de la configuration de sécurité
function initializeSecurityConfig() {
    // Valider la configuration
    const isValid = validateSecurityConfig();
    
    if (!isValid) {
        console.error('❌ Configuration de sécurité invalide!');
        return false;
    }
    
    // Afficher les avertissements
    displaySecurityWarnings();
    
    // Configuration réussie
    if (SECURITY_LOGGING.enabled) {
        console.log('✅ Configuration de sécurité initialisée avec succès');
    }
    
    return true;
}

// Export des configurations pour utilisation dans l'application
window.SECURITY_CONFIG = SECURITY_CONFIG;
window.GEMINI_API_CONFIG = GEMINI_API_CONFIG;
window.ADMIN_AUTH_CONFIG = ADMIN_AUTH_CONFIG;
window.SECURITY_LOGGING = SECURITY_LOGGING;
window.secureDecodeConfig = secureDecodeConfig;
window.validateSecurityConfig = validateSecurityConfig;
window.initializeSecurityConfig = initializeSecurityConfig;

// Initialiser automatiquement
document.addEventListener('DOMContentLoaded', function() {
    initializeSecurityConfig();
});
